#!/bin/bash

# 脚本：批量修改 Dockerfile 中的 Debian 镜像源
# 用途：解决 Docker 构建时 apt-get 下载慢的问题

echo "开始修复 Dockerfile 中的 Debian 镜像源..."

# 查找所有 Dockerfile
find . -name "*Dockerfile*" -o -name "*.dockerfile" | while read dockerfile; do
    echo "处理文件: $dockerfile"
    
    # 检查是否是基于 Debian 的镜像
    if grep -q "FROM.*python.*slim\|FROM.*debian\|FROM.*ubuntu" "$dockerfile"; then
        echo "  -> 发现基于 Debian/Ubuntu 的镜像，添加镜像源配置..."
        
        # 创建临时文件
        temp_file=$(mktemp)
        
        # 处理文件内容
        awk '
        /^FROM.*python.*slim/ || /^FROM.*debian/ || /^FROM.*ubuntu/ {
            print $0
            # 添加镜像源配置
            print ""
            print "# 配置 Debian 国内镜像源以加速包下载"
            print "RUN sed -i '\''s/deb.debian.org/mirrors.ustc.edu.cn/g'\'' /etc/apt/sources.list.d/debian.sources 2>/dev/null || true && \\"
            print "    sed -i '\''s/security.debian.org/mirrors.ustc.edu.cn/g'\'' /etc/apt/sources.list.d/debian.sources 2>/dev/null || true && \\"
            print "    sed -i '\''s|http://archive.ubuntu.com|https://mirrors.ustc.edu.cn|g'\'' /etc/apt/sources.list 2>/dev/null || true && \\"
            print "    sed -i '\''s|http://security.ubuntu.com|https://mirrors.ustc.edu.cn|g'\'' /etc/apt/sources.list 2>/dev/null || true"
            next
        }
        # 跳过已经存在的镜像源配置
        /配置.*镜像源/ || /mirrors\.ustc\.edu\.cn/ {
            skip_next = 1
            next
        }
        skip_next && /^RUN sed -i/ {
            skip_next = 0
            next
        }
        skip_next && /^    sed -i/ {
            next
        }
        skip_next && /^$/ {
            skip_next = 0
            next
        }
        {
            skip_next = 0
            print $0
        }
        ' "$dockerfile" > "$temp_file"
        
        # 检查是否有变化
        if ! cmp -s "$dockerfile" "$temp_file"; then
            mv "$temp_file" "$dockerfile"
            echo "  -> 已更新 $dockerfile"
        else
            rm "$temp_file"
            echo "  -> $dockerfile 无需更新"
        fi
    else
        echo "  -> 跳过 $dockerfile (非 Debian/Ubuntu 基础镜像)"
    fi
done

echo "完成！所有 Dockerfile 已处理。"

# 提供使用建议
echo ""
echo "建议的国内镜像源选择："
echo "1. 中科大镜像 (当前使用): mirrors.ustc.edu.cn"
echo "2. 阿里云镜像: mirrors.aliyun.com"
echo "3. 清华大学镜像: mirrors.tuna.tsinghua.edu.cn"
echo "4. 华为云镜像: mirrors.huaweicloud.com"
echo ""
echo "如需更换镜像源，请手动编辑相应的 Dockerfile 文件。"
